<template>
  <div class="group-message-config-original">

    <!-- 严格按照原始UI界面：群发设置卡片 -->
    <el-card class="group-message-card">
      <div slot="header" class="clearfix">
        <span style="font-size: 18px; font-weight: bold; color: #333333;">群发设置</span>
      </div>

      <!-- 小红书应用选择 -->
      <div style="margin-bottom: 15px;">
        <div style="margin-bottom: 8px;">
          <span style="font-size: 14px; color: #666666;">小红书应用</span>
        </div>
        <el-select
          v-model="selectedApp"
          placeholder="请选择要使用的小红书应用"
          @change="onAppSelectionChange"
          style="width: 100%; background: #F5F5F5; border-radius: 4px;"
        >
          <el-option
            v-for="app in xiaohongshuApps"
            :key="app.text"
            :label="app.text"
            :value="app.text"
          >
            <span style="float: left">{{ app.text }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">
              {{ app.method === 'keyword' ? '关键词' : '正则' }}
            </span>
          </el-option>
        </el-select>
      </div>

      <!-- 严格按照原始UI界面：只有发送间隔设置，没有消息内容输入框 -->
      <div style="margin-bottom: 15px;">
        <div style="margin-bottom: 8px;">
          <span style="font-size: 14px; color: #666666;">发送间隔(秒)</span>
        </div>
        <el-input
          v-model="intervalInput"
          placeholder="每条消息发送间隔时间（默认：10秒）"
          type="number"
          style="width: 100%; background: #F5F5F5; border-radius: 4px;"
        />
      </div>

      <!-- 严格按照原始UI界面：统计显示区域 -->
      <div class="statistics-section">
        <div class="stat-item" style="margin-bottom: 15px;">
          <span class="stat-label">已发送消息数：</span>
          <span class="stat-value sent-count">{{ sentMessageCount }}</span>
        </div>

        <div class="stat-item" style="margin-bottom: 15px;">
          <span class="stat-label">已处理控件数：</span>
          <span class="stat-value processed-count">{{ processedControlCount }}</span>
        </div>

        <div class="stat-item" style="margin-bottom: 15px;">
          <span class="stat-label">执行次数：</span>
          <span class="stat-value execution-count">{{ executionCount }}</span>
        </div>

        <div class="stat-item" style="margin-bottom: 15px;">
          <span class="stat-label">循环次数：</span>
          <span class="stat-value loop-count">{{ loopCount }}</span>
        </div>

        <!-- 严格按照原始UI界面：当前状态显示 -->
        <div class="stat-item" style="margin-bottom: 15px;">
          <span class="stat-label">当前状态：</span>
          <span class="stat-value current-status">{{ currentStatus }}</span>
        </div>

        <!-- 严格按照原始UI界面：自动保存设置 -->
        <div class="setting-item" style="margin-bottom: 15px;">
          <span class="setting-label" style="width: 120px; display: inline-block;">自动保存设置</span>
          <el-switch v-model="autoSave"></el-switch>
        </div>

        <!-- 严格按照原始UI界面：循环执行模式开关 -->
        <div class="setting-item" style="margin-bottom: 20px;">
          <span class="setting-label" style="width: 120px; display: inline-block;">循环执行模式</span>
          <el-switch v-model="loopMode"></el-switch>
        </div>

        <!-- 严格按照原始UI界面：按钮组 layout_weight="1" layout_weight="0.8" layout_weight="0.6" -->
        <div style="display: flex; gap: 4px;">
          <el-button
            type="primary"
            size="medium"
            @click="startScript"
            :disabled="isRunning"
            style="flex: 1; height: 50px; background: #FF4FB3FF; border-color: #FF4FB3FF; margin: 0;"
          >
            {{ startButtonText }}
          </el-button>
          <el-button
            type="success"
            size="medium"
            @click="restartScript"
            :disabled="!restartEnabled"
            style="flex: 0.8; height: 50px; background: #4CAF50; border-color: #4CAF50; margin: 0;"
          >
            再次开始
          </el-button>
          <el-button
            type="danger"
            size="medium"
            @click="stopScript"
            :disabled="!stopEnabled"
            style="flex: 0.6; height: 50px; background: #F44336; border-color: #F44336; margin: 0;"
          >
            终止
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 严格按照原始UI界面：操作日志卡片 -->
    <el-card class="log-card" style="margin-top: 10px;">
      <div slot="header" class="clearfix">
        <span style="font-size: 14px; font-weight: bold; color: #FF4FB3FF;">操作日志</span>
      </div>
      <div class="log-content" style="height: 150px; overflow-y: auto; background: #FAFAFA; padding: 10px; border-radius: 4px;">
        <div v-if="logMessages.length === 0" style="color: #666666; font-size: 12px;">
          等待操作...
        </div>
        <div v-else>
          <div
            v-for="(log, index) in logMessages"
            :key="index"
            style="color: #666666; font-size: 12px; line-height: 1.4; margin-bottom: 2px;"
          >
            {{ log }}
          </div>
        </div>
      </div>
    </el-card>



    <!-- 严格按照原始UI界面：使用说明卡片 -->
    <el-card class="usage-card" style="margin-top: 10px;">
      <div slot="header" class="clearfix">
        <span style="font-size: 14px; font-weight: bold; color: #FF4FB3FF;">使用说明</span>
      </div>
      <div class="usage-content" style="padding: 8px 0;">
        <!-- 严格按照原始脚本第161-174行的使用说明 -->
        <div class="usage-item">• 请先开启无障碍服务</div>
        <div class="usage-item">• 确保小红书应用已安装</div>
        <div class="usage-item">• 可自定义发送间隔时间</div>
        <div class="usage-item">• 脚本会自动记录所有群聊控件并循环点击</div>
        <div class="usage-item">• 自动排除系统消息、活动消息等无效控件</div>
        <div class="usage-item">• 智能滑动查找页面中的所有群聊</div>
        <div class="usage-item">• 执行过程中请勿手动操作手机</div>
        <div class="usage-item success">• 无需截屏权限，仅使用无障碍服务</div>
        <div class="usage-item warning">• 每次执行完成后自动退出小红书并休眠30秒(测试版)</div>
        <div class="usage-item loop">• 循环执行模式：开启后脚本会自动循环执行</div>
        <div class="usage-item loop">• 循环模式下：执行完成→退出小红书→休眠1小时→重新执行</div>
        <div class="usage-item primary">• 开始执行脚本：首次启动脚本</div>
        <div class="usage-item success">• 再次开始：重新从头开始执行，重置所有状态</div>
        <div class="usage-item danger">• 终止：完全终止脚本并重置所有状态</div>
      </div>
    </el-card>
  </div>
</template>

<script>
import io from 'socket.io-client'
import xiaohongshuAppSelector from '@/mixins/xiaohongshuAppSelector'

export default {
  name: 'GroupMessageConfigOriginal',
  mixins: [xiaohongshuAppSelector],
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    // 接收父组件传递的设备列表
    selectedDevices: {
      type: Array,
      default: () => []
    },
    // 接收父组件传递的在线设备信息
    onlineDevices: {
      type: Array,
      default: () => []
    },
    // 接收当前设备ID（用于多设备独立配置）
    deviceId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      // 严格按照原始UI界面的数据结构
      intervalInput: '10', // 对应 ui.intervalInput
      autoSave: true, // 对应 ui.autoSave
      loopMode: false, // 对应 ui.loopMode
      isRunning: false,

      // 严格按照原始UI界面的统计数据
      sentMessageCount: 0, // 对应 ui.sentCountText
      processedControlCount: 0, // 对应 ui.processedCountText
      executionCount: 0, // 对应 ui.executionCountText
      loopCount: 0, // 对应 ui.loopCountText
      currentStatus: '等待开始', // 对应 ui.currentStatusText

      // 严格按照原始UI界面的日志
      logMessages: [], // 对应 ui.logText

      // Socket连接
      socket: null,
      currentTaskId: null,
      // 保存执行时的真实logId和taskId
      currentLogId: null,

      // 选择的小红书应用
      selectedApp: ''
    }
  },

  watch: {
    // 监听配置变化
    intervalInput() {
      this.updateConfig()
    },
    loopMode() {
      this.updateConfig()
    },
    autoSave() {
      this.updateConfig()
    },
    selectedApp() {
      this.updateConfig()
    },
    // 监听父组件传递的配置
    value: {
      handler(newVal) {
        if (newVal && typeof newVal === 'object') {
          if (newVal.sendInterval !== undefined) {
            this.intervalInput = newVal.sendInterval.toString()
          }
          if (newVal.executionMode !== undefined) {
            this.loopMode = newVal.executionMode === 'loop'
          }
          if (newVal.autoSave !== undefined) {
            this.autoSave = newVal.autoSave
          }
        }
      },
      immediate: true,
      deep: true
    }
  },

  computed: {
    // 严格按照原始UI界面的按钮状态
    startButtonText() {
      return this.isRunning ? '正在执行...' : '开始执行脚本'
    },

    restartEnabled() {
      return !this.isRunning
    },

    stopEnabled() {
      return this.isRunning
    }
  },

  async mounted() {
    this.initializeSocket()
    this.addLog('UI界面初始化完成')

    // 监听全局实时状态事件（路由切换后的备用监听）
    this.$root.$on('xiaohongshu_realtime_status', (data) => {
      console.log('[GroupMessageConfig] 收到全局实时状态事件:', data)
      this.handleRealtimeStatus(data)
    })

    // 监听任务开始事件
    this.$root.$on('xiaohongshu-task-started', (data) => {
      console.log('[GroupMessageConfig] 收到任务开始事件:', data)
      if (data.functionType === 'groupMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[GroupMessageConfig] 循环群发任务开始，更新按钮状态')
        this.isRunning = true

        // 保存真实的logId和taskId
        if (data.logId) {
          this.currentLogId = data.logId
          console.log('[GroupMessageConfig] 保存logId:', this.currentLogId)
        }
        if (data.taskId) {
          this.currentTaskId = data.taskId
          console.log('[GroupMessageConfig] 保存taskId:', this.currentTaskId)
        }

        // 更新运行状态
        this.isRunning = true
        this.updateCurrentStatus('脚本执行中')
        this.addLog('=== 脚本开始执行 ===')

        // 重置统计数据
        this.sentMessageCount = 0
        this.processedControlCount = 0
        this.executionCount = 0
        this.loopCount = 0

        // 保存状态
        this.saveComponentState()
      }
    })

    // 监听任务停止事件
    this.$root.$on('xiaohongshu-task-stopped', (data) => {
      console.log('[GroupMessageConfig] 收到任务停止事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      if (functionType === 'groupMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[GroupMessageConfig] 循环群发任务停止，重置按钮状态')

        // 清空保存的logId和taskId
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[GroupMessageConfig] 已清空logId和taskId')

        this.handleScriptStopped()

        // 保存状态
        this.saveComponentState()
      }
    })

    // 监听组件任务停止事件（从执行日志页面停止触发）
    this.$root.$on('xiaohongshu-component-task-stopped', (data) => {
      console.log('🛑 [GroupMessageConfig] 收到组件任务停止事件:', data)
      console.log('🛑 [GroupMessageConfig] 当前设备ID:', this.deviceId)
      console.log('🛑 [GroupMessageConfig] 当前isRunning状态:', this.isRunning)

      const functionType = typeof data === 'string' ? data : data.functionType
      if (functionType === 'groupMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('🛑 [GroupMessageConfig] 循环群发任务从执行日志停止，重置按钮状态')
        console.log('🛑 [GroupMessageConfig] 停止前isRunning:', this.isRunning)

        // 清空保存的logId和taskId
        this.currentLogId = null
        this.currentTaskId = null
        console.log('🛑 [GroupMessageConfig] 已清空logId和taskId')

        this.handleScriptStopped()

        console.log('🛑 [GroupMessageConfig] 停止后isRunning:', this.isRunning)
        console.log('🛑 [GroupMessageConfig] 按钮文本应该是:', this.startButtonText)

        // 保存状态
        this.saveComponentState()
      } else {
        console.log('🛑 [GroupMessageConfig] 事件不匹配，忽略:', {
          functionType,
          expectedFunction: 'groupMessage',
          deviceId: data.deviceId,
          currentDeviceId: this.deviceId
        })
      }
    })

    // 监听脚本完成事件（连字符格式）
    this.$root.$on('xiaohongshu-script-completed', (data) => {
      console.log('[GroupMessageConfig] 收到脚本完成事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      if (functionType === 'groupMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[GroupMessageConfig] 循环群发脚本完成')

        this.isRunning = false
        this.updateCurrentStatus(data.status === 'success' ? '执行完成' : '执行失败')
        this.addLog(data.status === 'success' ? '✅ 脚本执行完成' : '❌ 脚本执行失败')

        // 保存状态
        this.saveComponentState()
      }
    })

    // 监听脚本完成事件（下划线格式）
    this.$root.$on('xiaohongshu_execution_completed', (data) => {
      console.log('[GroupMessageConfig] 收到脚本执行完成事件:', data)
      const functionType = typeof data === 'string' ? data : data.functionType
      if (functionType === 'groupMessage' && (!this.deviceId || data.deviceId === this.deviceId)) {
        console.log('[GroupMessageConfig] 循环群发脚本执行完成')

        this.isRunning = false
        this.updateCurrentStatus(data.status === 'success' ? '执行完成' : '执行失败')
        this.addLog(data.status === 'success' ? '✅ 脚本执行完成' : '❌ 脚本执行失败')

        // 清空保存的logId和taskId
        this.currentLogId = null
        this.currentTaskId = null
        console.log('[GroupMessageConfig] 脚本完成，已清空logId和taskId')

        // 保存状态
        this.saveComponentState()
      }
    })

    // 检查是否有正在运行的任务
    await this.checkRunningTasks()

    if (!this.isRunning) {
      this.addLog('请先开启无障碍服务，然后点击开始按钮')
    }

    // 注意：上面已经有了带设备ID检查的事件监听，这里不需要重复监听

    // 监听WebSocket事件
    if (this.$socket) {
      this.$socket.on('xiaohongshu_execution_completed', (data) => {
        console.log('[GroupMessageConfig] 收到WebSocket脚本执行完成事件:', data)
        if (data.deviceId === this.deviceId || !this.deviceId) {
          console.log('[GroupMessageConfig] 脚本执行完成，更新状态')

          this.isRunning = false
          this.updateCurrentStatus(data.status === 'success' ? '执行完成' : '执行失败')
          this.addLog(data.status === 'success' ? '✅ 脚本执行完成' : '❌ 脚本执行失败')

          // 保存状态
          this.saveComponentState()
        }
      })
    }

    // 恢复组件状态
    this.restoreComponentState()
  },

  beforeDestroy() {
    // 保存组件状态
    this.saveComponentState()

    // 清理Socket事件监听器（不断开连接，因为是全局连接）
    if (this.socket) {
      this.socket.off('xiaohongshu_task_update')
      this.socket.off('xiaohongshu_all_tasks_stopped')
      this.socket.off('script_stopped')
      this.socket.off('script_status')
      this.socket.off('xiaohongshu_realtime_status')
      console.log('[GroupMessageConfig] Socket事件监听器已清理')
    }

    // 清理事件监听
    this.$root.$off('xiaohongshu_realtime_status')
    this.$root.$off('xiaohongshu-task-started')
    this.$root.$off('xiaohongshu-task-stopped')
    this.$root.$off('xiaohongshu-component-task-stopped')
    this.$root.$off('xiaohongshu-script-completed')
    this.$root.$off('xiaohongshu_execution_completed')
  },

  methods: {
    // 严格按照原始UI界面的日志函数
    addLog(message) {
      const timestamp = new Date().toLocaleTimeString()
      const logMessage = `[${timestamp}] ${message}`
      this.logMessages.push(logMessage)

      // 保持最新的20条日志
      if (this.logMessages.length > 20) {
        this.logMessages.shift()
      }

      console.log(logMessage)
    },

    // 严格按照原始UI界面的状态更新函数
    updateCurrentStatus(status) {
      this.currentStatus = status
    },

    updateSentCount() {
      // 更新已发送消息数
    },

    updateProcessedCount() {
      // 更新已处理控件数
    },

    updateExecutionCount() {
      // 更新执行次数
    },

    updateLoopCount() {
      // 更新循环次数
    },

    // 初始化Socket连接
    initializeSocket() {
      // 使用全局Socket连接，而不是创建新的连接
      this.socket = this.$store.getters['socket/socket']

      if (!this.socket) {
        console.warn('[GroupMessageConfig] 全局Socket未连接，等待连接...')
        // 监听Socket连接状态变化
        this.$store.watch(
          (state) => state.socket.socket,
          (newSocket) => {
            if (newSocket) {
              console.log('[GroupMessageConfig] 检测到Socket连接，重新初始化监听器')
              this.socket = newSocket
              this.setupSocketListeners()
            }
          }
        )
        return
      }

      console.log('[GroupMessageConfig] 使用全局Socket连接')
      this.setupSocketListeners()
    },

    // 设置Socket事件监听器
    setupSocketListeners() {
      if (!this.socket) return

      console.log('[GroupMessageConfig] 设置Socket事件监听器')

      // 监听任务状态更新
      this.socket.on('xiaohongshu_task_update', (data) => {
        console.log('[GroupMessageConfig] Socket收到任务状态更新:', data)
        this.handleTaskUpdate(data)
      })

      // 监听所有任务停止
      this.socket.on('xiaohongshu_all_tasks_stopped', () => {
        console.log('[GroupMessageConfig] Socket收到所有任务停止事件')
        this.handleAllTasksStopped()
      })

      // 监听设备脚本停止事件
      this.socket.on('script_stopped', (data) => {
        console.log('[GroupMessageConfig] Socket收到脚本停止事件:', data)
        this.handleDeviceScriptStopped(data)
      })

      // 监听设备脚本执行状态
      this.socket.on('script_status', (data) => {
        console.log('[GroupMessageConfig] Socket收到脚本状态事件:', data)
        this.handleScriptStatus(data)
      })

      // 监听实时状态更新
      this.socket.on('xiaohongshu_realtime_status', (data) => {
        console.log('[GroupMessageConfig] Socket收到实时状态更新:', data)
        this.handleRealtimeStatus(data)
      })

      console.log('[GroupMessageConfig] Socket事件监听器设置完成')
    },

    // 处理任务更新
    handleTaskUpdate(data) {
      if (data.taskId === this.currentTaskId && data.params && data.params.function === 'groupMessage') {
        this.addLog(`任务更新: ${data.message}`)

        if (data.status === 'executing') {
          this.updateCurrentStatus('正在执行')
        } else if (data.status === 'sleeping') {
          this.updateCurrentStatus('休眠中')
        } else if (data.status === 'completed') {
          if (this.loopMode) {
            this.loopCount++
            this.updateCurrentStatus('循环执行中')
          } else {
            this.handleTaskStopped()
          }
        } else if (data.status === 'failed') {
          this.updateCurrentStatus('执行失败')
          this.$message.error('任务执行失败: ' + data.message)
        }
      }
    },

    // 处理所有任务停止
    handleAllTasksStopped() {
      if (this.isRunning) {
        this.addLog('📱 收到服务器停止信号')
        this.addLog('✅ 手机端脚本已停止执行')
        this.handleScriptStopped()
      }
    },

    // 处理设备脚本停止事件
    handleDeviceScriptStopped(data) {
      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        this.addLog(`📱 设备 ${data.deviceId} 脚本已停止`)
        this.handleScriptStopped()
      }
    },

    // 处理脚本状态更新
    handleScriptStatus(data) {
      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        this.addLog(`📱 设备状态: ${data.status} - ${data.message}`)

        if (data.status === 'stopped' || data.status === 'completed' || data.status === 'failed') {
          this.handleScriptStopped()
        } else if (data.status === 'running') {
          this.updateCurrentStatus('正在执行')
        }
      }
    },

    // 处理实时状态更新
    handleRealtimeStatus(data) {
      if (this.currentTaskId && data.taskId === this.currentTaskId) {
        // 更新统计数据
        if (data.sentMessageCount !== undefined) {
          this.sentMessageCount = data.sentMessageCount
        }
        if (data.processedControlCount !== undefined) {
          this.processedControlCount = data.processedControlCount
        }
        if (data.executionCount !== undefined) {
          this.executionCount = data.executionCount
        }
        if (data.loopCount !== undefined) {
          this.loopCount = data.loopCount
        }
        if (data.currentStatus) {
          this.updateCurrentStatus(data.currentStatus)
        }

        // 添加操作日志
        if (data.message) {
          this.addLog(data.message)
        }

        console.log('实时状态更新:', data)
      }
    },

    // 检查正在运行的任务
    async checkRunningTasks() {
      try {
        const response = await this.$http.get('/api/xiaohongshu/tasks')

        if (response.data.success && response.data.data.activeTasks) {
          const activeTasks = response.data.data.activeTasks

          // 查找群发消息功能的活跃任务
          const groupMessageTask = activeTasks.find(task =>
            task.function === 'groupMessage' &&
            (task.status === 'running' || task.status === 'executing')
          )

          if (groupMessageTask) {
            this.addLog('🔄 检测到正在运行的群发任务，正在恢复状态...')
            this.restoreTaskState(groupMessageTask)
          } else {
            this.addLog('✅ 当前没有正在运行的群发任务')
          }
        }
      } catch (error) {
        console.error('检查运行任务失败:', error)
        this.addLog('❌ 检查运行任务失败: ' + error.message)
      }
    },

    // 恢复任务状态
    restoreTaskState(task) {
      this.currentTaskId = task.id
      this.isRunning = true

      // 恢复配置
      if (task.config) {
        if (task.config.sendInterval) {
          this.intervalInput = task.config.sendInterval.toString()
        }
        if (task.config.executionMode) {
          this.loopMode = task.config.executionMode === 'loop'
        }
        if (task.config.autoSave !== undefined) {
          this.autoSave = task.config.autoSave
        }
      }

      // 恢复实时状态数据
      if (task.realtimeData) {
        this.sentMessageCount = task.realtimeData.sentMessageCount || 0
        this.processedControlCount = task.realtimeData.processedControlCount || 0
        this.executionCount = task.realtimeData.executionCount || 0
        this.loopCount = task.realtimeData.loopCount || 0

        if (task.realtimeData.currentStatus) {
          this.updateCurrentStatus(task.realtimeData.currentStatus)
        } else {
          this.updateCurrentStatus('正在执行')
        }

        this.addLog(`📊 已恢复实时数据: 消息${this.sentMessageCount} 控件${this.processedControlCount} 执行${this.executionCount} 循环${this.loopCount}`)
      } else {
        this.updateCurrentStatus('正在执行')
      }

      this.addLog(`✅ 已恢复任务状态: ${task.id}`)
      this.addLog('📊 实时状态数据将自动同步')

      // 触发配置变化事件，通知父组件
      this.updateConfig()
    },

    // 保存组件状态
    async saveComponentState() {
      try {
        const stateKey = `groupMessage_state_${this.deviceId || 'default'}`
        const state = {
          isRunning: this.isRunning,
          currentStatus: this.currentStatus,
          intervalInput: this.intervalInput,
          loopMode: this.loopMode,
          currentTaskId: this.currentTaskId,
          currentLogId: this.currentLogId,
          sentMessageCount: this.sentMessageCount,
          processedControlCount: this.processedControlCount,
          executionCount: this.executionCount,
          loopCount: this.loopCount,
          logMessages: this.logMessages,
          realtimeLogs: this.realtimeLogs
        }
        localStorage.setItem(stateKey, JSON.stringify(state))
        console.log('[GroupMessageConfig] 状态已保存:', state)
      } catch (error) {
        console.error('[GroupMessageConfig] 保存状态失败:', error)
      }
    },

    // 恢复组件状态
    async restoreComponentState() {
      try {
        const stateKey = `groupMessage_state_${this.deviceId || 'default'}`
        const savedState = localStorage.getItem(stateKey)
        if (savedState) {
          const state = JSON.parse(savedState)

          // 恢复状态
          this.isRunning = state.isRunning || false
          this.currentStatus = state.currentStatus || '等待开始'
          this.intervalInput = state.intervalInput || '10'
          this.loopMode = state.loopMode || false
          this.currentTaskId = state.currentTaskId || null
          this.currentLogId = state.currentLogId || null
          this.sentMessageCount = state.sentMessageCount || 0
          this.processedControlCount = state.processedControlCount || 0
          this.executionCount = state.executionCount || 0
          this.loopCount = state.loopCount || 0
          this.logMessages = state.logMessages || []
          this.realtimeLogs = state.realtimeLogs || []

          console.log('[GroupMessageConfig] 状态已恢复:', state)
        }
      } catch (error) {
        console.error('[GroupMessageConfig] 恢复状态失败:', error)
      }
    },

    // 严格按照原始UI界面的按钮事件
    async startScript() {
      if (this.isRunning) {
        this.$message.warning('脚本正在运行中，请等待完成')
        return
      }

      const sendInterval = parseInt(this.intervalInput) || 10

      if (isNaN(sendInterval) || sendInterval <= 0) {
        this.$message.error('请输入有效的发送间隔时间（大于0的数字）')
        return
      }

      try {
        console.log('[GroupMessageConfig] 开始执行循环群发脚本')

        // 构建配置参数
        const config = {
          sendInterval: sendInterval,
          executionMode: this.loopMode ? 'loop' : 'once',
          loopInterval: this.loopMode ? 60 : undefined
        }

        // 通过父组件的方法执行脚本
        this.$emit('execute-script', {
          functionType: 'groupMessage',
          config: config,
          deviceId: this.deviceId
        })

        this.addLog('=== 开始执行脚本 ===')
        this.addLog(`发送间隔: ${sendInterval}秒`)
        this.addLog(`执行模式: ${this.loopMode ? '循环执行' : '单次执行'}`)

        console.log('[GroupMessageConfig] 脚本执行请求已发送')
        // 注意：不在这里更新状态，等待任务开始事件来更新状态

      } catch (error) {
        console.error('[GroupMessageConfig] 启动脚本失败:', error)
        this.$message.error('启动脚本失败: ' + error.message)
        this.addLog('启动失败: ' + error.message)
      }
    },

    async restartScript() {
      // 如果脚本正在运行，先停止再重新开始
      if (this.isRunning) {
        this.$confirm('脚本正在运行中，是否要先终止当前脚本然后重新开始？', '确认重新开始', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(async () => {
          try {
            // 先停止当前脚本
            this.addLog('正在终止当前脚本...')
            const response = await this.$http.post('/api/xiaohongshu/stop')

            if (response.data.success) {
              this.addLog('✅ 当前脚本已终止')
              // 等待一下确保停止完成
              await new Promise(resolve => setTimeout(resolve, 1500))

              // 重置状态并重新开始
              this.handleScriptStopped()
              this.performRestart()
            } else {
              this.$message.error('停止失败，无法重新开始')
            }
          } catch (error) {
            this.$message.error('停止失败: ' + error.message)
          }
        }).catch(() => {
          // 用户取消
        })
        return
      }

      // 脚本未运行时的重新开始
      this.$confirm('确定要重新开始执行脚本吗？这将重置所有状态并从头开始执行。', '确认再次开始', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.performRestart()
      }).catch(() => {
        // 用户取消
      })
    },

    // 执行重新开始的逻辑
    performRestart() {
      // 完全重置所有状态到初始状态
      this.sentMessageCount = 0
      this.processedControlCount = 0
      this.executionCount = 0
      this.loopCount = 0

      this.updateCurrentStatus('重新开始')
      this.logMessages = []
      this.realtimeLogs = [] // 清空实时日志
      this.addLog('=== 重新开始脚本执行 ===')
      this.addLog('所有状态已重置，从头开始执行脚本')

      // 延迟一下再调用开始脚本，确保UI更新完成
      setTimeout(() => {
        this.startScript()
      }, 500)
    },

    async stopScript() {
      if (!this.isRunning) {
        this.$message.warning('脚本未在运行')
        return
      }

      this.$confirm('确定要完全终止脚本吗？这将终止手机端正在执行的脚本并重置所有状态。', '确认完全终止脚本', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        try {
          this.addLog('正在终止手机端脚本...')
          this.updateCurrentStatus('正在终止...')

          console.log('[GroupMessageConfig] 当前保存的logId:', this.currentLogId)
          console.log('[GroupMessageConfig] 当前保存的taskId:', this.currentTaskId)

          // 如果有设备ID，停止特定设备；否则停止所有任务
          let stopData = {}
          if (this.deviceId) {
            // 单设备停止，使用保存的真实logId和taskId
            console.log('[GroupMessageConfig] 停止特定设备:', this.deviceId)
            stopData = {
              deviceId: this.deviceId,
              taskId: this.currentTaskId || `xiaohongshu_groupMessage_${this.deviceId}`,
              logId: this.currentLogId || `xiaohongshu_groupMessage_${Date.now()}_${this.deviceId}`
            }
            console.log('[GroupMessageConfig] 停止数据:', stopData)
          } else {
            // 停止所有任务（兼容旧版本）
            console.log('[GroupMessageConfig] 停止所有循环群发任务')
          }

          // 立即更新前端状态
          this.isRunning = false
          this.updateCurrentStatus('已停止')
          this.addLog('✅ 正在停止脚本...')

          const response = await this.$http.post('/api/xiaohongshu/stop', stopData)

          if (response.data.success) {
            this.addLog('✅ 手机端脚本终止成功')
            this.addLog('📱 所有设备的脚本执行已停止')

            // 完全重置所有状态到初始状态
            this.handleScriptStopped()

            this.$message.success('脚本已完全终止并重置')

            // 通知父组件任务已停止
            this.$emit('task-stopped', {
              taskId: this.currentTaskId,
              function: 'groupMessage'
            })

            // 发送全局停止事件
            this.$root.$emit('xiaohongshu-task-stopped', {
              functionType: 'groupMessage',
              reason: 'manual',
              deviceId: this.deviceId
            })
          } else {
            this.$message.error('停止失败: ' + response.data.message)
            this.addLog('❌ 停止失败: ' + response.data.message)
          }
        } catch (error) {
          this.$message.error('停止失败: ' + error.message)
          this.addLog('❌ 停止失败: ' + error.message)
          console.error('[GroupMessageConfig] 停止脚本失败:', error)

          // 即使请求失败，也确保前端状态正确
          this.isRunning = false
          this.updateCurrentStatus('停止失败')
          this.handleScriptStopped()
        }
      }).catch(() => {
        // 用户取消
      })
    },

    // 处理脚本停止后的状态重置
    handleScriptStopped() {
      console.log('🔄 [GroupMessageConfig] handleScriptStopped 被调用')
      console.log('🔄 [GroupMessageConfig] 重置前isRunning:', this.isRunning)

      this.isRunning = false
      this.currentTaskId = null
      this.sentMessageCount = 0
      this.processedControlCount = 0
      this.executionCount = 0
      this.loopCount = 0

      this.updateCurrentStatus('等待开始')
      this.addLog('🔄 所有状态已重置')
      this.addLog('✅ 可以重新点击开始按钮从头执行脚本')

      console.log('🔄 [GroupMessageConfig] 重置后isRunning:', this.isRunning)
      console.log('🔄 [GroupMessageConfig] 按钮文本应该是:', this.startButtonText)

      // 强制触发响应式更新
      this.$forceUpdate()

      // 保存状态
      this.saveComponentState()
    },

    // 添加配置更新方法，与父组件通信
    updateConfig() {
      const config = {
        sendInterval: parseInt(this.intervalInput) || 10,
        executionMode: this.loopMode ? 'loop' : 'once',
        loopInterval: this.loopMode ? 60 : undefined,
        autoSave: this.autoSave,
        selectedApp: this.selectedApp || ''
      }

      this.$emit('input', config)
      this.$emit('update', config)
    },

    // 处理应用选择变化
    onAppSelectionChange(selectedApp) {
      console.log('选择的小红书应用:', selectedApp)
      this.selectedApp = selectedApp
      this.updateConfig()
    },

    // 监听配置变化
    onConfigChange() {
      this.$nextTick(() => {
        this.updateConfig()
      })
    },


  }
}
</script>

<style scoped>
/* 严格按照原始UI界面的样式 */
.group-message-config-original {
  padding: 15px;
  background: #FFFFFF;
}



/* 群发设置卡片样式 */
.group-message-card {
  border-radius: 8px;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.group-message-card .el-card__header {
  background: #FFFFFF;
  border-bottom: none;
  padding: 20px 20px 0 20px;
}

/* 统计显示区域样式 */
.statistics-section {
  padding: 10px 0;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #F0F0F0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

.stat-value {
  font-size: 14px;
  font-weight: bold;
  color: #FF4FB3FF;
}

.stat-value.sent-count {
  color: #4CAF50;
}

.stat-value.processed-count {
  color: #2196F3;
}

.stat-value.execution-count {
  color: #FF9800;
}

.stat-value.loop-count {
  color: #9C27B0;
}

.stat-value.current-status {
  color: #FF4FB3FF;
  font-size: 13px;
}

/* 设置项样式 */
.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.setting-label {
  font-size: 14px;
  color: #333333;
  font-weight: 500;
}

/* 日志卡片样式 */
.log-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.log-card .el-card__header {
  background: #F8F9FA;
  border-bottom: 1px solid #E9ECEF;
}

.log-content {
  font-family: 'Courier New', monospace;
}

/* 使用说明卡片样式 */
.usage-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.usage-card .el-card__header {
  background: #F8F9FA;
  border-bottom: 1px solid #E9ECEF;
}

.usage-content {
  line-height: 1.6;
}

.usage-item {
  font-size: 12px;
  color: #666666;
  margin-bottom: 4px;
  padding: 2px 0;
}

.usage-item.success {
  color: #4CAF50;
  font-weight: 500;
}

.usage-item.warning {
  color: #FF9800;
  font-weight: 500;
}

.usage-item.loop {
  color: #9C27B0;
  font-weight: 500;
}

.usage-item.primary {
  color: #2196F3;
  font-weight: 500;
}

.usage-item.danger {
  color: #F44336;
  font-weight: 500;
}

/* 按钮样式 */
.el-button {
  font-weight: bold;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.el-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.el-button:disabled {
  opacity: 0.6;
  transform: none !important;
  box-shadow: none !important;
}

/* 开关样式 */
.el-switch.is-checked .el-switch__core {
  background-color: #FF4FB3FF;
  border-color: #FF4FB3FF;
}

/* 输入框样式 */
.el-input__inner {
  border-radius: 6px;
  border: 1px solid #DCDFE6;
  transition: border-color 0.3s ease;
}

.el-input__inner:focus {
  border-color: #FF4FB3FF;
  box-shadow: 0 0 0 2px rgba(255, 79, 179, 0.2);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .group-message-config-original {
    padding: 5px;
  }

  .stat-item {
    flex-direction: column;
    align-items: flex-start;
    padding: 6px 0;
  }

  .stat-label {
    margin-bottom: 4px;
  }

  .el-button {
    height: 45px;
    font-size: 13px;
  }
}

/* 动画效果 */
.group-message-card, .log-card, .usage-card {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.log-content::-webkit-scrollbar {
  width: 6px;
}

.log-content::-webkit-scrollbar-track {
  background: #F1F1F1;
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb {
  background: #C1C1C1;
  border-radius: 3px;
}

.log-content::-webkit-scrollbar-thumb:hover {
  background: #A8A8A8;
}


</style>
