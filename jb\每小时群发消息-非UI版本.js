// 小红书每小时群发消息脚本 - 非UI版本
// 功能：启动小红书 -> 打开消息页面 -> 循环点击群聊控件 -> 群发消息
// 需要 Auto.js 4.1.1+ 版本支持

// 导入 KeyEvent 类
importClass(android.view.KeyEvent);

// ==================== 小红书应用启动器 ====================
/**
 * 启动指定名称的小红书应用
 * @param {string} appName - 要启动的小红书应用名称
 * @returns {boolean} - 启动是否成功
 */
function launchXiaohongshuByName(appName) {
    if (!appName || appName.trim() === '') {
        addLog("未指定小红书应用名称，使用默认启动方式");
        return launchXiaohongshuDefault();
    }

    addLog("正在启动小红书应用: " + appName);

    // 先按home键回到桌面
    home();
    sleep(2000);
    home();
    sleep(1000);

    // 获取屏幕尺寸
    var screenWidth = device.width;
    var screenHeight = device.height;

    addLog("屏幕尺寸: " + screenWidth + " x " + screenHeight);

    // 滑动函数
    function swipeLeft() {
        addLog("执行左滑动作");
        swipe(screenWidth * 0.8, screenHeight * 0.5, screenWidth * 0.2, screenHeight * 0.5, 500);
        sleep(1000);
    }

    function swipeRight() {
        addLog("执行右滑动作");
        swipe(screenWidth * 0.2, screenHeight * 0.5, screenWidth * 0.8, screenHeight * 0.5, 500);
        sleep(1000);
    }

    // 查找元素函数
    function findTargetElement() {
        return className("android.widget.TextView").text(appName).findOne(2000);
    }

    try {
        addLog("开始查找目标应用: " + appName);

        var targetElement = findTargetElement();
        var maxAttempts = 10; // 最多尝试10次滑动（左5次，右5次）
        var attempts = 0;

        // 如果没找到元素，开始滑动查找
        while (!targetElement && attempts < maxAttempts) {
            attempts++;
            addLog("第" + attempts + "次尝试滑动查找...");

            if (attempts <= 5) {
                // 前5次向左滑动
                swipeLeft();
            } else {
                // 后5次向右滑动
                swipeRight();
            }

            // 滑动后重新查找
            targetElement = findTargetElement();
        }

        if (targetElement) {
            addLog("找到目标应用：" + targetElement.text());

            // 获取元素位置信息
            var bounds = targetElement.bounds();
            addLog("应用位置：x=" + bounds.centerX() + ", y=" + bounds.centerY());

            // 点击元素
            targetElement.click();
            addLog("已点击目标应用");

            // 等待应用启动
            sleep(3000);

            // 检查是否成功启动小红书
            var currentPkg = currentPackage();
            if (currentPkg && currentPkg.includes("xhs")) {
                addLog("✅ 小红书应用启动成功");
                return true;
            } else {
                addLog("⚠️ 应用已点击，但可能未成功启动小红书，当前应用: " + (currentPkg || "未知"));
                // 即使包名检查失败，也认为启动成功，因为可能是分身应用包名不同
                return true;
            }

        } else {
            addLog("❌ 经过" + attempts + "次滑动后仍未找到目标应用：" + appName);

            // 显示当前界面所有TextView元素用于调试
            addLog("当前界面的TextView元素：");
            var allTextViews = className("android.widget.TextView").find();
            for (var i = 0; i < allTextViews.length && i < 10; i++) {
                var text = allTextViews[i].text();
                if (text && text.trim() !== '') {
                    addLog("TextView " + i + ": " + text);
                }
            }

            // 如果找不到指定应用，尝试使用默认启动方式
            addLog("尝试使用默认启动方式");
            return launchXiaohongshuDefault();
        }

    } catch (error) {
        addLog("❌ 启动应用过程出错：" + error.message);
        addLog("尝试使用默认启动方式");
        return launchXiaohongshuDefault();
    }
}

/**
 * 默认的小红书启动方式（兼容旧版本）
 * @returns {boolean} - 启动是否成功
 */
function launchXiaohongshuDefault() {
    addLog("使用默认方式启动小红书...");

    try {
        // 先尝试通过应用名启动
        launchApp("小红书");
        sleep(3000);

        // 检查是否成功启动
        var currentPkg = currentPackage();
        if (currentPkg && currentPkg.includes("xhs")) {
            addLog("✅ 默认方式启动小红书成功");
            return true;
        }

        // 如果失败，尝试坐标点击方式
        addLog("尝试坐标点击方式启动");
        home();
        sleep(2000);

        var screenWidth = device.width;
        var screenHeight = device.height;
        var clickX = screenWidth * 0.5;
        var clickY = screenHeight * 0.65;

        click(clickX, clickY);
        sleep(3000);

        addLog("✅ 小红书启动完成（默认方式）");
        return true;

    } catch (error) {
        addLog("❌ 默认启动方式失败: " + error.message);
        return false;
    }
}

// 创建存储器
let storage;
try {
    storage = storages.create("XiaohongshuGroupMessage");
} catch (e) {
    console.log("存储器创建失败，使用默认存储: " + e.message);
    storage = {
        get: function(key, defaultValue) {
            console.log("获取存储: " + key + " = " + defaultValue);
            return defaultValue;
        },
        put: function(key, value) {
            console.log("存储: " + key + " = " + value);
        }
    };
}

// 配置参数
const CONFIG = {
    sendInterval: 10,        // 发送间隔（秒）
    loopMode: true,         // 循环执行模式
    autoSave: true          // 自动保存设置
};

// 全局变量
let isRunning = false;
let isPaused = false;
let logMessages = [];
let currentThread = null;
let sentMessageCount = 0; // 已发送消息计数器
let processedControlCount = 0; // 已处理控件计数器
let executionCount = 0; // 执行次数计数器
let isLoopMode = false; // 循环模式标识
let loopCount = 0; // 循环执行次数计数器

// 添加日志函数
function addLog(message) {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = "[" + timestamp + "] " + message;
    logMessages.push(logMessage);

    // 保持最新的20条日志
    if (logMessages.length > 20) {
        logMessages.shift();
    }

    console.log(logMessage);
}

// 实时状态上报函数
function sendRealtimeStatus(statusData) {
    try {
        // 构造要发送的数据
        const data = {
            deviceId: "DEVICE_ID_PLACEHOLDER", // 将在脚本生成时替换
            taskId: "TASK_ID_PLACEHOLDER", // 将在脚本生成时替换
            sentMessageCount: sentMessageCount || 0,
            processedControlCount: processedControlCount || 0,
            executionCount: executionCount || 0,
            loopCount: loopCount || 0,
            currentStatus: statusData.currentStatus || "",
            message: statusData.message || "",
            timestamp: new Date().toISOString()
        };

        // 手动合并statusData的属性（兼容旧版Auto.js）
        if (statusData) {
            for (let key in statusData) {
                if (statusData.hasOwnProperty(key)) {
                    data[key] = statusData[key];
                }
            }
        }

        // 使用线程发送HTTP请求到服务器
        threads.start(function() {
            try {
                const response = http.postJson("http://192.168.1.91:3002/api/xiaohongshu/realtime-status", data, {
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: 3000
                });

                if (response && response.statusCode === 200) {
                    console.log("实时状态上报成功");
                } else {
                    console.log("实时状态上报失败: " + (response ? response.statusCode : "无响应"));
                }
            } catch (e) {
                console.log("实时状态上报网络错误: " + e.message);
            }
        });

        // 同时记录本地日志
        const logMessage = '实时状态: ' + JSON.stringify(statusData);
        addLog(logMessage);
    } catch (e) {
        // 静默处理错误
        console.log("实时状态上报错误: " + e.message);
    }
}

// 发送执行完成状态到服务器
function sendExecutionCompleteStatus(status, message) {
    try {
        threads.start(function() {
            try {
                const response = http.postJson("http://192.168.1.91:3002/api/xiaohongshu/status", {
                    taskId: "TASK_ID_PLACEHOLDER",
                    deviceId: "DEVICE_ID_PLACEHOLDER",
                    stage: "completed",
                    status: status,
                    progress: status === "success" ? 100 : 0,
                    message: message,
                    timestamp: new Date().toISOString()
                }, {
                    headers: {
                        "Content-Type": "application/json"
                    },
                    timeout: 5000
                });

                if (response && response.statusCode === 200) {
                    console.log("执行完成状态上报成功");
                } else {
                    console.log("执行完成状态上报失败: " + (response ? response.statusCode : "无响应"));
                }
            } catch (e) {
                console.log("执行完成状态上报网络错误: " + e.message);
            }
        });
    } catch (e) {
        console.log("执行完成状态上报错误: " + e.message);
    }
}

// 更新发送消息数显示
function updateSentCount() {
    // 发送实时状态更新
    sendRealtimeStatus({
        sentMessageCount: sentMessageCount,
        message: '已发送消息数: ' + sentMessageCount
    });
}

// 更新已处理控件数显示
function updateProcessedCount() {
    // 发送实时状态更新
    sendRealtimeStatus({
        processedControlCount: processedControlCount,
        message: '已处理控件数: ' + processedControlCount
    });
}

// 更新执行次数显示
function updateExecutionCount() {
    // 发送实时状态更新
    sendRealtimeStatus({
        executionCount: executionCount,
        message: '执行次数: ' + executionCount
    });
}

// 更新循环次数显示
function updateLoopCount() {
    // 发送实时状态更新
    sendRealtimeStatus({
        loopCount: loopCount,
        message: '循环次数: ' + loopCount
    });
}

// 更新当前状态显示
function updateCurrentStatus(status) {
    addLog('状态更新: ' + status);
    // 发送实时状态更新
    sendRealtimeStatus({
        currentStatus: status,
        message: '状态更新: ' + status
    });
}

// 检查脚本是否应该继续运行
function checkPause() {
    if (!isRunning) {
        addLog("脚本已被终止，停止执行");
        throw new Error("脚本已被用户终止");
    }
}

// 检查无障碍服务状态
function checkAccessibilityService() {
    if (!auto.service) {
        addLog("❌ 无障碍服务未开启，请手动开启");
        toast("请先开启无障碍服务");
        return false;
    }
    addLog("✅ 无障碍服务已开启");
    return true;
}

// 终止脚本函数
function stopScript() {
    if (!isRunning) {
        addLog("脚本未在运行");
        return;
    }

    // 完全终止脚本执行
    isRunning = false;
    isPaused = false;

    // 如果有正在运行的线程，尝试中断
    if (currentThread) {
        try {
            currentThread.interrupt();
            addLog("脚本线程已中断");
        } catch (e) {
            addLog("中断线程时出错: " + e.message);
        }
        // 清空线程引用
        currentThread = null;
    }

    // 完全重置所有状态到初始状态
    sentMessageCount = 0;
    processedControlCount = 0;
    executionCount = 0;
    loopCount = 0;
    isLoopMode = false;

    // 清空日志并添加重置信息
    logMessages = [];
    addLog("脚本已完全终止，所有状态已重置");
    addLog("脚本已被用户完全终止并重置");
    toast("脚本已完全终止并重置");
}

// 重新开始脚本函数
function restartScript() {
    if (isRunning) {
        addLog("脚本正在运行中，请先终止再重新开始");
        return;
    }

    const sendInterval = CONFIG.sendInterval;

    if (!checkAccessibilityService()) {
        return;
    }

    // 确保清理之前的线程状态
    if (currentThread) {
        try {
            currentThread.interrupt();
            addLog("清理之前的线程状态");
        } catch (e) {
            addLog("清理线程状态时出错: " + e.message);
        }
        currentThread = null;
    }

    // 完全重置所有状态到初始状态
    sentMessageCount = 0;
    processedControlCount = 0;
    executionCount = 0;
    loopCount = 0;
    isLoopMode = CONFIG.loopMode;

    // 更新运行状态
    isRunning = true;
    isPaused = false;

    // 清空日志并开始新的执行
    logMessages = [];
    addLog("=== 重新开始脚本执行 ===");
    addLog("所有状态已重置，从头开始执行脚本");
    addLog("检查系统环境...");

    // 延迟启动新线程
    setTimeout(() => {
        // 在新线程中执行脚本
        currentThread = threads.start(function() {
            try {
                addLog("启动新的执行线程");
                executeScript(sendInterval);
            } catch (e) {
                addLog("脚本执行出错: " + e.message);
                console.error("脚本执行出错: " + e.message);

                // 脚本执行出错后关闭小红书应用
                addLog("脚本执行出错，休眠5秒后关闭小红书应用");
                sleep(5000);
                exitXiaohongshu();
            } finally {
                // 恢复状态
                isRunning = false;
                isPaused = false;
                currentThread = null;
                addLog("脚本执行线程已结束");
            }
        });
    }, 1000); // 延迟1秒启动
}

// 开始脚本函数
function startScript() {
    if (isRunning) {
        addLog("脚本正在运行中，请等待完成");
        return;
    }

    const sendInterval = CONFIG.sendInterval;

    if (!checkAccessibilityService()) {
        return;
    }

    // 确保清理之前的线程状态
    if (currentThread) {
        try {
            currentThread.interrupt();
            addLog("清理之前的线程状态");
        } catch (e) {
            addLog("清理线程状态时出错: " + e.message);
        }
        currentThread = null;
    }

    // 完全重置所有状态到初始状态
    sentMessageCount = 0;
    processedControlCount = 0;
    executionCount = 0;
    loopCount = 0;
    isLoopMode = CONFIG.loopMode;

    // 更新运行状态
    isRunning = true;
    isPaused = false;

    // 清空日志并开始新的执行
    logMessages = [];
    addLog("=== 开始新的脚本执行 ===");
    addLog("所有状态已重置，从头开始执行脚本");
    addLog("检查系统环境...");

    // 延迟启动新线程
    setTimeout(() => {
        // 在新线程中执行脚本
        currentThread = threads.start(function() {
            try {
                addLog("启动新的执行线程");
                executeScript(sendInterval);
            } catch (e) {
                addLog("脚本执行出错: " + e.message);
                console.error("脚本执行出错: " + e.message);

                // 脚本执行出错后关闭小红书应用
                addLog("脚本执行出错，休眠5秒后关闭小红书应用");
                sleep(5000);
                exitXiaohongshu();
            } finally {
                // 恢复状态
                isRunning = false;
                isPaused = false;
                currentThread = null;
                addLog("脚本执行线程已结束");
            }
        });
    }, 1000); // 延迟1秒启动
}

// 启动小红书应用（统一入口函数）
function launchXiaohongshu(selectedApp) {
    addLog("=== 开始启动小红书应用 ===");

    if (selectedApp && selectedApp.trim() !== '') {
        addLog("使用指定应用启动: " + selectedApp);
        return launchXiaohongshuByName(selectedApp);
    } else {
        addLog("未指定应用名称，使用默认启动方式");
        return launchXiaohongshuDefault();
    }
}

// 确保在主页
function ensureInHomePage() {
    if (!desc("首页").exists()) {
        backToHome();
    }
    toast("已回到主页");
}

// 返回主页
function backToHome() {
    while (!desc("首页").exists()) {
        back();
        sleep(1000);
    }
}

// 打开消息页面
function openMessagePage() {
    toast("正在尝试打开消息页面...");
    updateCurrentStatus("点击消息按钮");

    // 尝试通过坐标点击
    toast("尝试通过坐标点击消息按钮");
    let screenWidth = device.width;
    let screenHeight = device.height;

    // 点击底部导航栏消息按钮位置
    console.log(screenWidth * 0.7, screenHeight *0.95)
    click(screenWidth * 0.7, screenHeight *0.95);
    sleep(2000);

    return checkMessagePageOpened();
}

// 检查消息页面是否成功打开
function checkMessagePageOpened() {
    updateCurrentStatus("检查消息页面");
    // 验证是否成功打开消息页面
    if (textContains("发现群聊").findOne(3000) ||
        textContains("新增关注").findOne(3000)) {
        toast("成功打开消息页面");
        updateCurrentStatus("消息页面已打开");
        analyzeAndClickTargetControls();
        return true;
    }

    toast("未能打开消息页面，再次尝试");
    updateCurrentStatus("重试打开消息页面");
    // 再次尝试点击
    click(device.width * 0.7, device.height *0.9);
    sleep(2000);

    if (textContains("发现群聊").findOne(3000) ||
        textContains("新增关注").findOne(3000)) {
        toast("成功打开消息页面");
        updateCurrentStatus("消息页面已打开");

        // 成功打开消息页面后，分析并点击目标控件
        analyzeAndClickTargetControls();

        return true;
    }

    toast("无法打开消息页面");
    updateCurrentStatus("打开消息页面失败");
    return false;
}

// 滑动到页面顶部
function scrollToTop() {
    addLog("开始滑动到页面顶部");
    let maxScrollAttempts = 20; // 最多尝试20次
    let scrollAttempt = 0;

    while (scrollAttempt < maxScrollAttempts) {
        scrollAttempt++;

        try {
            // 获取滑动前的页面内容
            let beforeContent = className("android.widget.TextView").find()
                .filter(e => e.visibleToUser() && e.text())
                .map(e => e.text()).join("");

            // 向上滑动
            swipe(device.width / 2, device.height * 0.3, device.width / 2, device.height * 0.7, 800);
            sleep(1500); // 等待页面稳定

            // 获取滑动后的页面内容
            let afterContent = className("android.widget.TextView").find()
                .filter(e => e.visibleToUser() && e.text())
                .map(e => e.text()).join("");

            // 如果内容没有变化，说明已经到顶部
            if (beforeContent === afterContent) {
                addLog("已滑动到页面顶部，总共滑动 " + scrollAttempt + " 次");
                break;
            }

            addLog("第 " + scrollAttempt + " 次向上滑动");

        } catch (e) {
            addLog("滑动到顶部时出错: " + e.message);
            break;
        }
    }

    if (scrollAttempt >= maxScrollAttempts) {
        addLog("滑动到顶部达到最大尝试次数: " + maxScrollAttempts);
    }

    // 额外等待确保页面稳定
    sleep(2000);
}

// 分析当前页面控件并点击目标控件
function analyzeAndClickTargetControls() {
    addLog("开始分析当前页面控件...");
    toast("开始分析页面控件");
    updateCurrentStatus("分析页面控件");

    // 等待页面完全加载
    sleep(3000);

    try {
        // 第一步：滑动到页面顶部
        addLog("第一步：滑动到页面顶部");
        toast("滑动到页面顶部");
        updateCurrentStatus("滑动到页面顶部");
        scrollToTop();

        // 第二步：记录所有RecyclerView控件
        addLog("第二步：开始记录所有RecyclerView控件");
        toast("开始记录控件");
        updateCurrentStatus("记录页面控件");
        let allTargetControls = [];
        let recordedControlIds = []; // 用于去重的数组

        // 记录当前页面的控件
        function recordCurrentPageControls() {
            let recyclerViews = className("androidx.recyclerview.widget.RecyclerView").find();
            let currentPageCount = 0;

            for (let i = 0; i < recyclerViews.length; i++) {
                let recyclerView = recyclerViews[i];

                // 在当前RecyclerView中查找目标控件
                let childControls = recyclerView.find(id("0_resource_name_obfuscated").className("android.view.ViewGroup").depth(15));

                for (let j = 0; j < childControls.length; j++) {
                    let control = childControls[j];
                    if (control.visibleToUser() && control.contentDescription) {
                        // 检查控件是否包含需要排除的文本
                        let controlDescription = control.contentDescription;
                        let shouldExclude = false;

                        // 检查是否包含排除的关键词
                        if (controlDescription.includes("移出了群聊") ||
                            controlDescription.includes("系统消息") ||
                            controlDescription.includes("活动消息")||
                            controlDescription.includes("，，")) {
                            shouldExclude = true;
                            addLog("排除控件: " + controlDescription.split(',')[0] + " (包含排除关键词)");
                        }

                        // 如果不需要排除，则记录该控件
                        if (!shouldExclude) {
                            // 使用contentDescription作为唯一标识符进行去重
                            let controlId = control.contentDescription;
                            if (recordedControlIds.indexOf(controlId) === -1) {
                                recordedControlIds.push(controlId);
                                allTargetControls.push({
                                    control: control,
                                    bounds: control.bounds(),
                                    contentDescription: control.contentDescription,
                                    recordedAt: new Date().getTime()
                                });
                                currentPageCount++;
                                addLog("记录控件 " + allTargetControls.length + ": " + controlId.split(',')[0]);
                            }
                        }
                    }
                }
            }

            return currentPageCount;
        }

        // 检查页面是否还能继续下滑
        function canScrollDown() {
            try {
                // 获取滑动前的页面内容
                let beforeContent = className("android.widget.TextView").find()
                    .filter(e => e.visibleToUser() && e.text())
                    .map(e => e.text()).join("");

                // 执行一次小幅度滑动测试
                swipe(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.6, 300);
                sleep(1000);

                // 获取滑动后的页面内容
                let afterContent = className("android.widget.TextView").find()
                    .filter(e => e.visibleToUser() && e.text())
                    .map(e => e.text()).join("");

                // 滑回去
                swipe(device.width / 2, device.height * 0.6, device.width / 2, device.height * 0.7, 300);
                sleep(1000);

                // 如果内容有变化，说明还能滑动
                return beforeContent !== afterContent;
            } catch (e) {
                return false;
            }
        }

        // 记录顶部页面的控件
        addLog("记录顶部页面的控件");
        updateCurrentStatus("记录顶部页面控件");
        let initialCount = recordCurrentPageControls();
        addLog("顶部页面记录到 " + initialCount + " 个新控件");

        // 第三步：向下滑动并记录控件，直到滑到底部
        addLog("第三步：开始向下滑动并记录控件");
        toast("开始向下滑动记录");
        updateCurrentStatus("开始向下滑动记录控件");
        let swipeCount = 0;
        let maxSwipes = 50; // 防止无限循环

        while (canScrollDown() && swipeCount < maxSwipes) {
            swipeCount++;
            addLog("第 " + swipeCount + " 次下滑");
            toast("第 " + swipeCount + " 次下滑记录");
            updateCurrentStatus("第 " + swipeCount + " 次下滑记录控件");

            // 下滑页面
            swipe(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.3, 800);
            sleep(2000); // 等待页面稳定

            // 记录当前页面的新控件
            let newCount = recordCurrentPageControls();
            addLog("第 " + swipeCount + " 次下滑后记录到 " + newCount + " 个新控件");
        }

        addLog("滑动完成，总共下滑 " + swipeCount + " 次");
        addLog("总共记录到 " + allTargetControls.length + " 个目标控件");
        updateCurrentStatus("控件记录完成，共记录 " + allTargetControls.length + " 个控件");

        if (allTargetControls.length === 0) {
            addLog("未找到符合条件的目标控件");
            updateCurrentStatus("未找到符合条件的目标控件");
            return;
        }

        // 第四步：使用记录的数据循环点击每个目标控件
        addLog("第四步：开始循环点击记录的控件");
        toast("开始循环点击控件");
        updateCurrentStatus("开始循环点击记录的控件");

        for (let k = 0; k < allTargetControls.length; k++) {
            let targetControlData = allTargetControls[k];

            addLog("点击第 " + (k + 1) + "/" + allTargetControls.length + " 个目标控件");
            toast("点击第 " + (k + 1) + "/" + allTargetControls.length + " 个控件");
            updateCurrentStatus("点击第 " + (k + 1) + "/" + allTargetControls.length + " 个目标控件");

            try {
                // 获取控件的contentDescription并提取群名
                console.log(targetControlData.contentDescription.split(',')[0]);
                let qunName = targetControlData.contentDescription.split(',')[0];

                if (!qunName) {
                    let errorMsg = "第 " + (k + 1) + " 个控件没有contentDescription或无法提取群名";
                    addLog("错误: " + errorMsg);
                    toast("错误: " + errorMsg);
                    throw new Error(errorMsg);
                }

                addLog("提取的群名: " + qunName);

                // 分析当前页面文本中是否包含群名（支持滑动到底部+滑动到顶部查找）
                addLog("正在分析当前页面文本是否包含群名: " + qunName);
                updateCurrentStatus("查找群聊: " + qunName);
                let pageContainsGroupName = false;

                // 检查页面文本的函数
                function checkPageForGroupName() {
                    let allTextElements = className("android.widget.TextView").find();
                    for (let textElement of allTextElements) {
                        if (textElement.visibleToUser() && textElement.text()) {
                            let textContent = textElement.text();
                            if (textContent.includes(qunName)) {
                                return textContent;
                            }
                        }
                    }
                    return null;
                }

                // 检查页面是否还能继续滑动的函数
                function canSwipeDown() {
                    try {
                        // 获取滑动前的页面内容
                        let beforeContent = className("android.widget.TextView").find()
                            .filter(e => e.visibleToUser() && e.text())
                            .map(e => e.text()).join("");

                        // 执行一次小幅度滑动测试
                        swipe(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.6, 300);
                        sleep(1000);

                        // 获取滑动后的页面内容
                        let afterContent = className("android.widget.TextView").find()
                            .filter(e => e.visibleToUser() && e.text())
                            .map(e => e.text()).join("");

                        // 滑回去
                        swipe(device.width / 2, device.height * 0.6, device.width / 2, device.height * 0.7, 300);
                        sleep(1000);

                        // 如果内容有变化，说明还能滑动
                        return beforeContent !== afterContent;
                    } catch (e) {
                        return false;
                    }
                }

                function canSwipeUp() {
                    try {
                        // 获取滑动前的页面内容
                        let beforeContent = className("android.widget.TextView").find()
                            .filter(e => e.visibleToUser() && e.text())
                            .map(e => e.text()).join("");

                        // 执行一次小幅度滑动测试
                        swipe(device.width / 2, device.height * 0.3, device.width / 2, device.height * 0.4, 300);
                        sleep(1000);

                        // 获取滑动后的页面内容
                        let afterContent = className("android.widget.TextView").find()
                            .filter(e => e.visibleToUser() && e.text())
                            .map(e => e.text()).join("");

                        // 滑回去
                        swipe(device.width / 2, device.height * 0.4, device.width / 2, device.height * 0.3, 300);
                        sleep(1000);

                        // 如果内容有变化，说明还能滑动
                        return beforeContent !== afterContent;
                    } catch (e) {
                        return false;
                    }
                }

                // 第一阶段：首次检查当前页面
                addLog("首次分析当前页面文本是否包含群名: " + qunName);
                let foundText = checkPageForGroupName();
                if (foundText) {
                    pageContainsGroupName = true;
                    addLog("在初始页面中找到群名: " + foundText);
                    toast("找到群名: " + qunName);
                } else {
                    // 第二阶段：下滑查找直到底部
                    addLog("开始下滑查找，滑动到页面底部");
                    toast("开始下滑查找群名");
                    updateCurrentStatus("下滑查找群聊: " + qunName);
                    let downSwipeCount = 0;

                    while (!pageContainsGroupName && canSwipeDown() && downSwipeCount < 20) {
                        downSwipeCount++;
                        addLog("第 " + downSwipeCount + " 次下滑查找群名: " + qunName);
                        toast("第 " + downSwipeCount + " 次下滑查找");
                        updateCurrentStatus("第 " + downSwipeCount + " 次下滑查找: " + qunName);

                        // 下滑页面
                        swipe(device.width / 2, device.height * 0.7, device.width / 2, device.height * 0.3, 800);
                        sleep(2000); // 等待页面稳定

                        // 检查当前页面
                        foundText = checkPageForGroupName();
                        if (foundText) {
                            pageContainsGroupName = true;
                            addLog("在第 " + downSwipeCount + " 次下滑后找到群名: " + foundText);
                            toast("找到群名: " + qunName);
                            break;
                        }
                    }

                    if (!pageContainsGroupName) {
                        addLog("下滑到底部仍未找到群名，总共下滑 " + downSwipeCount + " 次");

                        // 第三阶段：上滑查找直到顶部
                        addLog("开始上滑查找，滑动到页面顶部");
                        toast("开始上滑查找群名");
                        let upSwipeCount = 0;

                        while (!pageContainsGroupName && canSwipeUp() && upSwipeCount < 20) {
                            upSwipeCount++;
                            addLog("第 " + upSwipeCount + " 次上滑查找群名: " + qunName);
                            toast("第 " + upSwipeCount + " 次上滑查找");

                            // 上滑页面
                            swipe(device.width / 2, device.height * 0.3, device.width / 2, device.height * 0.7, 800);
                            sleep(2000); // 等待页面稳定

                            // 检查当前页面
                            foundText = checkPageForGroupName();
                            if (foundText) {
                                pageContainsGroupName = true;
                                addLog("在第 " + upSwipeCount + " 次上滑后找到群名: " + foundText);
                                toast("找到群名: " + qunName);
                                break;
                            }
                        }

                        if (!pageContainsGroupName) {
                            addLog("上滑到顶部仍未找到群名，总共上滑 " + upSwipeCount + " 次");
                        }
                    }
                }

                // 如果经过所有尝试后仍未找到群名，跳过本次循环
                if (!pageContainsGroupName) {
                    let warnMsg = "经过完整的页面滑动查找（下滑到底部 + 上滑到顶部）仍未找到群名 '" + qunName + "'，跳过本次循环";
                    addLog("警告: " + warnMsg);
                    toast("警告: 未找到群名，跳过本次循环");
                    continue; // 跳过本次循环，继续下一个控件
                }

                addLog("页面文本验证通过，开始重新获取群名控件坐标");
                console.log(targetControlData.contentDescription);

                // 重新获取该群名的坐标信息
                let updatedControl = null;
                let updatedCoordinates = null;

                try {
                    // 重新查找包含该群名的控件
                    addLog("重新查找群名 '" + qunName + "' 对应的控件");

                    // 方法1：通过contentDescription重新查找
                    let allControls = id("0_resource_name_obfuscated").className("android.view.ViewGroup").depth(15).find();
                    for (let control of allControls) {
                        if (control.visibleToUser() && control.contentDescription &&
                            control.contentDescription.includes(qunName)) {
                            updatedControl = control;
                            addLog("通过contentDescription重新找到群名控件");
                            break;
                        }
                    }

                    // 方法2：如果方法1失败，通过文本查找附近的控件
                    if (!updatedControl) {
                        addLog("尝试通过文本查找群名控件");
                        let textElements = className("android.widget.TextView").find();
                        for (let textElement of textElements) {
                            if (textElement.visibleToUser() && textElement.text() &&
                                textElement.text().includes(qunName)) {
                                // 查找该文本元素附近的目标控件
                                let textBounds = textElement.bounds();
                                for (let control of allControls) {
                                    if (control.visibleToUser()) {
                                        let controlBounds = control.bounds();
                                        // 检查控件是否在文本附近（允许一定的误差范围）
                                        if (Math.abs(controlBounds.centerY() - textBounds.centerY()) < 100) {
                                            updatedControl = control;
                                            addLog("通过文本位置找到附近的群名控件");
                                            break;
                                        }
                                    }
                                }
                                if (updatedControl) break;
                            }
                        }
                    }

                    // 获取更新后的坐标
                    if (updatedControl) {
                        updatedCoordinates = {
                            x: updatedControl.bounds().centerX(),
                            y: updatedControl.bounds().centerY()
                        };
                        addLog("成功获取更新后的坐标: (" + updatedCoordinates.x + ", " + updatedCoordinates.y + ")");
                        console.log("更新后的坐标:", updatedCoordinates.x, updatedCoordinates.y);
                    } else {
                        // 如果重新查找失败，使用记录的坐标
                        updatedCoordinates = {
                            x: targetControlData.bounds.centerX(),
                            y: targetControlData.bounds.centerY()
                        };
                        addLog("重新查找失败，使用记录的坐标: (" + updatedCoordinates.x + ", " + updatedCoordinates.y + ")");
                        console.log("使用记录的坐标:", updatedCoordinates.x, updatedCoordinates.y);
                    }

                } catch (error) {
                    addLog("重新获取坐标时出错: " + error.message);
                    // 出错时使用记录的坐标
                    updatedCoordinates = {
                        x: targetControlData.bounds.centerX(),
                        y: targetControlData.bounds.centerY()
                    };
                    addLog("出错时使用记录的坐标: (" + updatedCoordinates.x + ", " + updatedCoordinates.y + ")");
                }

                // 点击控件（使用更新后的坐标）
                addLog("开始点击第 " + (k + 1) + " 个控件，群名: " + qunName);
                updateCurrentStatus("点击群聊: " + qunName);
                click(updatedCoordinates.x, updatedCoordinates.y);

                console.log("最终点击坐标:", updatedCoordinates.x, updatedCoordinates.y);

                addLog("成功点击控件: (" + updatedCoordinates.x + ", " + updatedCoordinates.y + ") (群名: " + qunName + ")");

                // 更新已处理控件计数器
                processedControlCount++;
                updateProcessedCount();
                addLog("已处理控件数: " + processedControlCount);

                // 等待10秒
                addLog("等待10秒...");
                sleep(10000);
                if(checkAndHandleGroupChatSuccess(qunName)){
                     addLog("返回上一页");
                    // back();

                    click(device.width * 0.05, device.height * 0.05);
                    sleep(2000);

                    // 检查是否需要重新等待页面加载
                    sleep(1000);

                }
                // 返回上一页

            } catch (e) {
                addLog("处理第 " + (k + 1) + " 个控件时发生错误: " + e.message);
                toast("错误: " + e.message);

                // 终止脚本运行
                addLog("脚本因错误终止运行");
                throw e; // 重新抛出错误，终止整个脚本
            }
        }

        addLog("脚本已终止");
    } catch (error) {
        addLog("分析页面控件时出错: " + error.message);
        toast("分析控件失败: " + error.message);
    }
}

// 检查是否成功加入群聊
function checkJoinGroupChatSuccess(qunName) {
    console.log("=== 检查是否成功加入群聊: " + qunName + " ===");

    try {
        // 等待页面加载完成
        sleep(2000);

        // 检查是否存在特定的成功加入群聊文本
        console.log("检查页面是否包含加入群聊成功的文本...");

        let foundSuccessIndicator = false;
        let successText = "";
        let maxAttempts = 2; // 最多尝试2次（初始检查 + 滑动后检查）

        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            console.log("第" + attempt + "次检查页面文本...");

            // 查找所有文本控件
            let allTexts = className("android.widget.TextView").find();
            for (let textElement of allTexts) {
                if (textElement.visibleToUser() && textElement.text()) {
                    let text = textElement.text();
                    console.log("检查文本: " + text);

                    // 检查是否包含"你加入了群聊，可上划查看群聊历史消息"
                    if (text.includes("你加入了群聊，可上划查看群聊历史消息")) {
                        console.log("发现加入群聊成功文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                    }

                    // 检查是否包含"条通知 展开"
                    if (text.includes("条通知 展开")) {
                        console.log("发现'条通知 展开'文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                    }
                    // 检查是否包含"条通知"
                    if (text.includes("条通知")) {
                        console.log("发现'条通知'文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                    }
                    // 检查是否包含"展开"
                    if (text.includes("展开")) {
                        console.log("发现'展开'文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                    }
                    if (text.includes(":")) {
                        console.log("发现':'文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                    }
                    if (text.includes("群主")) {
                        console.log("发现'群主'文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                    }
                    if (text.includes("有人@你")) {
                        console.log("发现'有人@你'文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                    }
                     if (text.includes("管理员")) {
                        console.log("发现'管理员'文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                    }
                     if (text.includes("在线")) {
                        console.log("发现'在线'文本: " + text);
                        foundSuccessIndicator = true;
                        successText = text;
                        break;
                     }
                }
            }

            // 如果找到了，直接退出循环
            if (foundSuccessIndicator) {
                break;
            }

            // 如果是第一次尝试且没找到，向上滑动页面
            if (attempt < maxAttempts) {
                console.log("第" + attempt + "次检查未找到目标文本，向上滑动页面...");
                toast("未找到目标文本，向上滑动页面");

                // 向上滑动页面
                let screenWidth = device.width;
                let screenHeight = device.height;
                swipe(screenWidth / 2, screenHeight * 0.7, screenWidth / 2, screenHeight * 0.3, 500);
                sleep(1500); // 等待滑动完成和页面稳定
            }
        }

        if (foundSuccessIndicator) {
            console.log("检测到加入群聊成功，匹配文本: " + successText);
            toast("检测到成功加入群聊");
            return true;
        } else {
            console.log("经过" + maxAttempts + "次检查仍未检测到加入群聊成功的指定文本");
            toast("未检测到加入群聊成功");
            return false;
        }

    } catch (error) {
        console.log("检查加入群聊状态时出错: " + error);
        toast("检查加入状态失败: " + error);
        return false;
    }
}

// 执行加入群聊成功后的三步坐标点击操作
function executeThreeStepClicks() {
    console.log("=== 执行加入群聊成功后的三步坐标点击操作 ===");

    try {
        // 获取屏幕尺寸
        let screenWidth = device.width;
        let screenHeight = device.height;

        console.log("屏幕尺寸: " + screenWidth + " x " + screenHeight);

        // 在执行三步操作前，先清空输入框并点击指定坐标
        console.log("清空输入框并点击指定坐标");
        addLog("清空输入框并点击指定坐标");

        // 查找输入框并清空
        let inputBox = className("android.widget.EditText").findOne(3000);
        if (inputBox && inputBox.visibleToUser()) {
            inputBox.setText("");
            console.log("输入框已清空");
            addLog("输入框已清空");
        } else {
            console.log("未找到输入框");
            addLog("未找到输入框");
        }

        // 点击坐标(屏幕宽度-50, 屏幕高度*0.3)
        let screenWidth1 = device.width;
        let screenHeight1 = device.height;
        swipe(screenWidth1 / 2, screenHeight1 * 0.3, screenWidth1 / 2, screenHeight1 * 0.7, 500);
        sleep(1500); // 等待滑动完成和页面稳定
        sleep(1500); // 等待响应

        // 第一步：点击坐标（屏幕宽度的0.8，高度的0.9）
        let step1X = screenWidth * 0.8;
        let step1Y = screenHeight * 0.95;
        console.log("第一步：点击坐标 (" + Math.round(step1X) + ", " + Math.round(step1Y) + ")");
        toast("第一步：点击坐标 (" + Math.round(step1X) + ", " + Math.round(step1Y) + ")");
        click(step1X, step1Y);
        sleep(1500); // 等待响应

        // 第二步：点击坐标（屏幕宽度的0.08，屏幕高度的0.79）
        let step2X = screenWidth * 0.08;
        let step2Y = screenHeight * 0.79;
        console.log("第二步：点击坐标 (" + Math.round(step2X) + ", " + Math.round(step2Y) + ")");
        toast("第二步：点击坐标 (" + Math.round(step2X) + ", " + Math.round(step2Y) + ")");
        click(step2X, step2Y);
        sleep(1500); // 等待响应

        // 第三步：点击坐标（屏幕宽度的0.9，屏幕高度的0.55）
        let step3X = screenWidth * 0.9;
        let step3Y = screenHeight * 0.63;
        console.log("第三步：点击坐标 (" + Math.round(step3X) + ", " + Math.round(step3Y) + ")");
        toast("第三步：点击坐标 (" + Math.round(step3X) + ", " + Math.round(step3Y) + ")");
        click(step3X, step3Y);
        sleep(1500); // 等待响应

        console.log("三步坐标点击操作执行完成");
        toast("三步坐标点击操作完成");
        return true;

    } catch (error) {
        console.log("执行三步坐标点击时出错: " + error);
        toast("执行坐标点击失败: " + error);
        return false;
    }
}

// 检查并处理群聊加入成功后的操作
function checkAndHandleGroupChatSuccess(qunName) {
    console.log("=== 检查并处理群聊加入成功后的操作 ===");

    try {
        // 检查是否成功加入群聊
        if (checkJoinGroupChatSuccess(qunName)) {
            console.log("确认加入群聊成功，开始执行后续操作");
            toast("加入群聊成功，执行后续操作");
            updateCurrentStatus("加入群聊成功: " + qunName);

            // 执行三步坐标点击操作
            if (executeThreeStepClicks()) {

                console.log("群聊加入成功后的所有操作执行完成");
                toast("所有操作执行完成");
                updateCurrentStatus("群聊操作完成: " + qunName);
                return true;
            } else {
                console.log("执行后续操作失败");
                toast("执行后续操作失败");
                updateCurrentStatus("群聊操作失败: " + qunName);
                return false;
            }
        } else {
            console.log("未确认加入群聊成功，跳过后续操作");
            toast("未确认加入群聊成功");
            return false;
        }

    } catch (error) {
        console.log("处理群聊加入成功后的操作时出错: " + error);
        toast("处理操作失败: " + error);
        return false;
    }
}

// 退出小红书应用
function exitXiaohongshu() {
    addLog("正在退出小红书应用...");
    toast("退出小红书应用");

    try {
        // 方法1：按Home键回到桌面
        addLog("按Home键回到桌面");
        home();
        sleep(2000);

        // 方法2：尝试关闭小红书应用
        let xiaohongshuPackages = [
            "com.xingin.xhs",
            "com.xingin.xhs.clone",
            "com.xingin.xhs.dual"
        ];

        for (let packageName of xiaohongshuPackages) {
            try {
                if (app.openAppSetting(packageName)) {
                    addLog("尝试通过设置关闭应用: " + packageName);
                    sleep(1000);
                }
            } catch (e) {
                // 忽略错误，继续尝试下一个
            }
        }

        // 方法3：强制回到桌面
        home();
        sleep(1000);
        home();
        sleep(2000);

        addLog("✅ 小红书应用已退出");
        toast("小红书应用已退出");
        return true;

    } catch (error) {
        addLog("❌ 退出小红书应用时出错: " + error.message);
        toast("退出应用失败: " + error.message);
        return false;
    }
}

// 权限检查函数
function checkPermissions() {
    addLog("检查系统权限...");

    // 检查无障碍服务
    if (!auto.service) {
        addLog("❌ 无障碍服务未开启");
        toast("请先开启无障碍服务");
        return false;
    }
    addLog("✅ 无障碍服务已开启");
    addLog("✅ 权限检查完成");

    return true;
}

// 单次执行函数（原executeScript的核心逻辑）
function executeSingleRun(sendInterval) {
    addLog("=== 开始单次执行 ===");
    addLog("发送间隔: " + sendInterval + "秒");

    try {
        // 保持屏幕常亮
        device.keepScreenOn();

        // 检查权限
        if (!checkPermissions()) {
            addLog("权限检查失败，停止执行");
            return false;
        }

        checkPause(); // 检查是否暂停

        // 启动小红书
        addLog("正在启动小红书应用...");
        updateCurrentStatus("启动小红书");
        var selectedApp = (typeof serverParams !== 'undefined' && serverParams.selectedApp) ? serverParams.selectedApp : '';
        if (!launchXiaohongshu(selectedApp)) {
            addLog("❌ 启动小红书失败");
            return false;
        }

        addLog("✅ 小红书启动成功");
        checkPause(); // 检查是否暂停

        // 确保在主页
        addLog("确保在主页...");
        updateCurrentStatus("确保在主页");
        ensureInHomePage();
        checkPause(); // 检查是否暂停

        // 打开消息页面
        addLog("正在打开消息页面...");
        updateCurrentStatus("打开消息页面");
        if (!openMessagePage()) {
            addLog("❌ 无法打开消息页面");
            return false;
        }

        addLog("✅ 消息页面打开成功");

        // 更新执行次数
        executionCount++;
        updateExecutionCount();

        addLog("=== 单次执行完成 ===");
        return true;

    } catch (error) {
        if (error.message.includes("用户终止")) {
            addLog("脚本被用户终止");
            updateCurrentStatus("用户终止");
            return false;
        } else {
            addLog("❌ 单次执行过程中出错: " + error.message);
            updateCurrentStatus("执行出错");
            console.error("executeSingleRun error:", error);
            return false;
        }
    }
}

// 主执行函数（支持循环模式）
function executeScript(sendInterval) {
    addLog("=== 开始执行脚本 ===");

    // 获取循环模式状态
    isLoopMode = CONFIG.loopMode;
    addLog("循环模式: " + (isLoopMode ? "开启" : "关闭"));

    if (isLoopMode) {
        // 循环执行模式
        addLog("进入循环执行模式");
        executeLoopMode(sendInterval);
    } else {
        // 单次执行模式
        addLog("进入单次执行模式");
        let success = executeSingleRun(sendInterval);
        if (success) {
            updateCurrentStatus("脚本执行完成");
            addLog("=== 脚本执行完成 ===");

            // 发送执行完成状态到服务器
            sendExecutionCompleteStatus("success", "脚本执行完成");

            // 脚本执行完成后关闭小红书应用
            addLog("脚本执行完成，休眠5秒后关闭小红书应用");
            updateCurrentStatus("脚本执行完成，休眠5秒后关闭小红书应用");
            sleep(5000);
            exitXiaohongshu();
        } else {
            updateCurrentStatus("执行失败");
            addLog("=== 脚本执行失败 ===");

            // 发送执行失败状态到服务器
            sendExecutionCompleteStatus("error", "脚本执行失败");

            // 脚本执行失败后关闭小红书应用
            addLog("脚本执行失败，休眠5秒后关闭小红书应用");
            updateCurrentStatus("脚本执行失败，休眠5秒后关闭小红书应用");
            sleep(5000);
            exitXiaohongshu();
        }
    }
}

// 循环执行模式函数
function executeLoopMode(sendInterval) {
    addLog("=== 开始循环执行模式 ===");

    while (isRunning) {
        try {
            // 增加循环次数
            loopCount++;
            updateLoopCount();
            addLog("循环次数: " + loopCount);
            addLog("开始第 " + loopCount + " 次循环执行");
            updateCurrentStatus("第 " + loopCount + " 次循环执行");

            // 执行单次脚本
            let success = executeSingleRun(sendInterval);

            if (!success) {
                addLog("第 " + loopCount + " 次循环执行失败");
                if (!isRunning) {
                    addLog("脚本被用户终止，退出循环");
                    break;
                }
                // 如果不是用户终止，继续下一次循环
                addLog("将在1小时后重试");
            } else {
                addLog("第 " + loopCount + " 次循环执行成功");
            }

            // 检查是否被用户终止
            if (!isRunning) {
                addLog("脚本被用户终止，退出循环");
                break;
            }

            // 退出小红书应用
            addLog("退出小红书应用");
            updateCurrentStatus("退出小红书应用");
            exitXiaohongshu();

            // 检查是否被用户终止
            if (!isRunning) {
                addLog("脚本被用户终止，退出循环");
                break;
            }

            // 休眠1小时（3600秒）
            addLog("开始休眠1小时...");
            updateCurrentStatus("休眠中 (1小时)");
            toast("脚本将休眠1小时后继续执行");

            // 分段休眠，每10秒检查一次是否被终止
            let sleepTime = 3600; // 1小时 = 3600秒
            // let sleepTime = 120; // 10秒
            let sleepInterval = 10; // 每10秒检查一次
            let sleptTime = 0;

            while (sleptTime < sleepTime && isRunning) {
                sleep(sleepInterval * 1000); // 休眠10秒
                sleptTime += sleepInterval;

                // 更新休眠状态显示
                let remainingMinutes = Math.ceil((sleepTime - sleptTime) / 60);
                updateCurrentStatus("休眠中 (剩余" + remainingMinutes + "分钟)");
            }

            if (!isRunning) {
                addLog("休眠期间脚本被用户终止，退出循环");
                break;
            }

            addLog("休眠结束，准备开始下一次循环");

        } catch (error) {
            if (error.message.includes("用户终止")) {
                addLog("循环执行被用户终止");
                updateCurrentStatus("用户终止");
                sendExecutionCompleteStatus("error", "循环执行被用户终止");
                break;
            } else {
                addLog("❌ 循环执行过程中出错: " + error.message);
                console.error("executeLoopMode error:", error);
                updateCurrentStatus("执行出错");
                sendExecutionCompleteStatus("error", "循环执行过程中出错: " + error.message);

                // 如果出错且脚本仍在运行，等待一段时间后继续
                if (isRunning) {
                    addLog("将在10分钟后重试");
                    updateCurrentStatus("出错，10分钟后重试");

                    // 分段休眠10分钟
                    let errorSleepTime = 600; // 10分钟 = 600秒
                    let sleepInterval = 10;
                    let sleptTime = 0;

                    while (sleptTime < errorSleepTime && isRunning) {
                        sleep(sleepInterval * 1000);
                        sleptTime += sleepInterval;

                        let remainingMinutes = Math.ceil((errorSleepTime - sleptTime) / 60);
                        updateCurrentStatus("出错重试中 (剩余" + remainingMinutes + "分钟)");
                    }
                }
            }
        }
    }

    addLog("=== 循环执行模式结束 ===");
    updateCurrentStatus("循环执行结束");

    // 发送循环执行完成状态到服务器
    sendExecutionCompleteStatus("success", "循环执行结束");
}

// 脚本初始化和自动启动
addLog("=== 小红书群发消息脚本 - 非UI版本 ===");
addLog("脚本初始化完成");
addLog("配置信息:");
addLog("- 发送间隔: " + CONFIG.sendInterval + "秒");
addLog("- 循环模式: " + (CONFIG.loopMode ? "开启" : "关闭"));
addLog("- 自动保存: " + (CONFIG.autoSave ? "开启" : "关闭"));

// 检查无障碍服务并自动启动脚本
if (checkAccessibilityService()) {
    addLog("准备自动启动脚本...");
    setTimeout(() => {
        startScript();
    }, 3000); // 延迟3秒启动
} else {
    addLog("请手动开启无障碍服务后重新运行脚本");
}